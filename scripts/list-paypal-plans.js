// Script to list all PayPal plans in the database
import { database } from '../lib/db/index.js';
import { paypalPlans } from '../lib/db/schema.js';

async function listPayPalPlans() {
  try {
    console.log('Fetching PayPal plans from database...\n');
    
    const plans = await database
      .select()
      .from(paypalPlans)
      .orderBy(paypalPlans.createdAt);

    if (plans.length === 0) {
      console.log('No PayPal plans found in database.');
      return;
    }

    console.log(`Found ${plans.length} PayPal plan(s):\n`);
    
    plans.forEach((plan, index) => {
      console.log(`${index + 1}. Plan Type: ${plan.planType}`);
      console.log(`   PayPal Plan ID: ${plan.paypalPlanId}`);
      console.log(`   Name: ${plan.name}`);
      console.log(`   Price: ${plan.price} ${plan.currency}`);
      console.log(`   Credits: ${plan.credits}`);
      console.log(`   Active: ${plan.isActive}`);
      console.log(`   Created: ${plan.createdAt}`);
      console.log('');
    });

  } catch (error) {
    console.error('Error fetching PayPal plans:', error);
  }
}

// Run the script
listPayPalPlans();
