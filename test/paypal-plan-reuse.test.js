// Test for PayPal plan reuse functionality
import { describe, it, expect, beforeEach } from 'vitest';
import { database } from '../lib/db/index.js';
import { paypalPlans } from '../lib/db/schema.js';
import { getOrCreatePayPalPlan } from '../lib/paypal/subscriptions.js';
import { eq } from 'drizzle-orm';

describe('PayPal Plan Reuse', () => {
  beforeEach(async () => {
    // Clean up test data
    await database.delete(paypalPlans);
  });

  it('should create a new plan when none exists', async () => {
    // Mock PayPal API response
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        id: 'P-TEST-PLAN-ID-123',
        name: 'Premium Plan',
        status: 'ACTIVE'
      })
    });

    const result = await getOrCreatePayPalPlan('PREMIUM');
    
    expect(result.success).toBe(true);
    expect(result.isNew).toBe(true);
    expect(result.planId).toBe('P-TEST-PLAN-ID-123');

    // Verify plan was saved to database
    const savedPlans = await database
      .select()
      .from(paypalPlans)
      .where(eq(paypalPlans.planType, 'PREMIUM'));
    
    expect(savedPlans).toHaveLength(1);
    expect(savedPlans[0].paypalPlanId).toBe('P-TEST-PLAN-ID-123');
  });

  it('should reuse existing plan when available', async () => {
    // First, create a plan manually in the database
    await database.insert(paypalPlans).values({
      id: 'test-id-1',
      planType: 'PREMIUM',
      paypalPlanId: 'P-EXISTING-PLAN-123',
      name: 'Premium Plan',
      description: 'Premium subscription with 1000 credits per month',
      price: '9.99',
      currency: 'USD',
      interval: 'MONTH',
      credits: 1000,
      isActive: true,
    });

    const result = await getOrCreatePayPalPlan('PREMIUM');
    
    expect(result.success).toBe(true);
    expect(result.isNew).toBe(false);
    expect(result.planId).toBe('P-EXISTING-PLAN-123');

    // Verify no additional plans were created
    const allPlans = await database
      .select()
      .from(paypalPlans)
      .where(eq(paypalPlans.planType, 'PREMIUM'));
    
    expect(allPlans).toHaveLength(1);
  });

  it('should create different plans for different types', async () => {
    // Mock PayPal API responses
    global.fetch = vi.fn()
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'P-PREMIUM-PLAN-123',
          name: 'Premium Plan',
          status: 'ACTIVE'
        })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          id: 'P-PRO-PLAN-456',
          name: 'Pro Plan',
          status: 'ACTIVE'
        })
      });

    const premiumResult = await getOrCreatePayPalPlan('PREMIUM');
    const proResult = await getOrCreatePayPalPlan('PRO');
    
    expect(premiumResult.success).toBe(true);
    expect(premiumResult.isNew).toBe(true);
    expect(premiumResult.planId).toBe('P-PREMIUM-PLAN-123');

    expect(proResult.success).toBe(true);
    expect(proResult.isNew).toBe(true);
    expect(proResult.planId).toBe('P-PRO-PLAN-456');

    // Verify both plans exist in database
    const allPlans = await database.select().from(paypalPlans);
    expect(allPlans).toHaveLength(2);
  });
});
