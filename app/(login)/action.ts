'use server'
import { verifyTurnstile } from '@/core/lib/turnstile';
import { createNewUser } from '@/core/services/users';



export async function signUp(prevState: unknown, formData: FormData) {
  // type-casting here for convenience
  // in practice, you should validate your inputs
  const {email, password} = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }
  const token = formData.get('token') as string;
  //check token
  const checkRes = await verifyTurnstile(token);
  if (!checkRes) {
    return { status:'failed', error: new Error('Invalidturnstile token'), email, password, state:prevState}
  }
  
  const { user, msg} = await createNewUser({email, password});
  if (!user) {
    return { status:'failed', error: new Error(msg), email, password, state:prevState}
  }

  //set session
  return { status: 'ok', message: 'Sign up successful', user: { id: user.id, email: user.email } }
}
