import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  try {
    // User cancelled the subscription process
    return NextResponse.redirect(new URL('/dashboard?cancelled=true', req.url));
  } catch (error) {
    console.error('Error handling PayPal subscription cancellation:', error);
    return NextResponse.redirect(new URL('/dashboard?error=internal_error', req.url));
  }
}
