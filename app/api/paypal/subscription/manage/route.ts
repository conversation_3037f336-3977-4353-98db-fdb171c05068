import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/core/auth';
import { database } from '@/core/db/index';
import { subscriptions } from '@/core/db/schema';
import { eq } from 'drizzle-orm';
import { cancelPayPalSubscription, getPayPalSubscription } from '@/core/paypal/subscriptions';

// Get user's PayPal subscription details
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's subscription from database
    const subscriptionResult = await database.select()
      .from(subscriptions)
      .where(eq(subscriptions.userId, session.user.id))
      .limit(1);

    const subscription = subscriptionResult[0];

    if (!subscription || !subscription.paypalSubscriptionId) {
      return NextResponse.json({ error: 'No PayPal subscription found' }, { status: 404 });
    }

    // Get PayPal subscription details
    const paypalResult = await getPayPalSubscription(subscription.paypalSubscriptionId);

    if (!paypalResult.success) {
      return NextResponse.json({ error: paypalResult.error }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      subscription: {
        id: subscription.id,
        status: subscription.status,
        type: subscription.type,
        creditsRemaining: subscription.creditsRemaining,
        creditsGrantedPerMonth: subscription.creditsGrantedPerMonth,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        paypalSubscriptionId: subscription.paypalSubscriptionId,
        paymentProvider: subscription.paymentProvider,
      },
      paypalDetails: paypalResult.data,
    });

  } catch (error) {
    console.error('Error getting PayPal subscription:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Cancel PayPal subscription
export async function DELETE(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { reason } = await req.json();

    // Get user's subscription from database
    const subscriptionResult = await database.select()
      .from(subscriptions)
      .where(eq(subscriptions.userId, session.user.id))
      .limit(1);

    const subscription = subscriptionResult[0];

    if (!subscription || !subscription.paypalSubscriptionId) {
      return NextResponse.json({ error: 'No PayPal subscription found' }, { status: 404 });
    }

    // Cancel PayPal subscription
    const cancelResult = await cancelPayPalSubscription(
      subscription.paypalSubscriptionId,
      reason || 'User requested cancellation'
    );

    if (!cancelResult.success) {
      return NextResponse.json({ error: cancelResult.error }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Subscription cancelled successfully',
    });

  } catch (error) {
    console.error('Error cancelling PayPal subscription:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
