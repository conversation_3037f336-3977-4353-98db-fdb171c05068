'use server'
import { auth } from "@/core/auth";
import { database } from "@/core/db/index";
import { subscriptions } from "@/core/db/schema";
import { eq } from "drizzle-orm";

export async function GET() {
    const session = await auth()
    if (!session?.user) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    if (!session.user.id) {
      return new Response(JSON.stringify({ error: "User not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" }
      });
    }
    const userSubscriptionResult = await database.select().from(subscriptions).where(eq(subscriptions.userId, session.user.id)).limit(1);
    const userSubscription = userSubscriptionResult[0];
    if (!userSubscription) {
      return new Response(JSON.stringify({ error: "User subscription not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" }
      });
    }
    return new Response(JSON.stringify({ credits: userSubscription.creditsRemaining }));
}