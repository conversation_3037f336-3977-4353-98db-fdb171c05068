import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/core/auth';
import { database } from '@/core/db/index';
import { users } from '@/core/db/schema';
import { compare, hash } from 'bcryptjs';
import { eq } from 'drizzle-orm';

export async function POST(req: NextRequest) {
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const { currentPassword, newPassword, confirmPassword } = await req.json();

  if (!currentPassword || !newPassword || !confirmPassword) {
    return NextResponse.json({ error: 'All fields are required.' }, { status: 400 });
  }
  if (newPassword !== confirmPassword) {
    return NextResponse.json({ error: 'New passwords do not match.' }, { status: 400 });
  }

  const userResult = await database.select().from(users).where(eq(users.id, session.user.id)).limit(1);
  const user = userResult[0];
  if (!user) {
    return NextResponse.json({ error: 'User not found.' }, { status: 404 });
  }

  const isValid = await compare(currentPassword, user.passwordHash as string);
  if (!isValid) {
    return NextResponse.json({ error: 'Current password is incorrect.' }, { status: 400 });
  }

  const hashed = await hash(newPassword, 10);
  await database.update(users)
    .set({ passwordHash: hashed })
    .where(eq(users.id, user.id));

  return NextResponse.json({ success: true });
} 