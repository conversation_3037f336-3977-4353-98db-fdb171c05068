'use client'
import { redirect } from "next/navigation";
import { But<PERSON> } from "@/core/components/ui/button";
import { useEffect, useState } from "react";
import { DialogContent, DialogHeader, DialogTitle, Dialog, DialogFooter } from "@/core/components/ui/dialog";
import { Input } from "@/core/components/ui/input";
import { useRouter } from "next/navigation";
import { signOut } from "next-auth/react";

const DeleteAccountButton = ({email}: {email: string}) => {
    const [open, setOpen] = useState(false);
    const [confirmText, setConfirmText] = useState('');
    const router = useRouter();
    const deleteAccount = async () => {
        if (confirmText !== email) {
            return;
        }
        const response = await fetch('/api/user/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email: confirmText }),
        });
        if (!response.ok) {
            throw new Error('Failed to delete account');
        }
        signOut()
        router.push('/');
    }
    useEffect(()=>{
        console.log(confirmText)
    },[confirmText])
    return (    
        <>
        <Button variant="destructive" onClick={() => setOpen(true)}>Delete Account</Button>
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Delete Account</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                    <p>Are you sure you want to delete your account? This action cannot be undone.  Type your email to confirm.</p>
                    <Input placeholder="Type your email to confirm" value={confirmText} onChange={(e) => setConfirmText(e.target.value)} />
                </div>
                <DialogFooter>
                    <Button disabled={confirmText !== email} variant="destructive" onClick={deleteAccount}>Delete</Button>
                    <Button onClick={() => setOpen(false)}>Cancel</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
        </>
    )
}

export default DeleteAccountButton;