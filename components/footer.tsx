'use client';

export function Footer() {
  const footerSections = [
    {
      title: '關於ANKER',
      links: [
        '關於我們',
        '服務說明',
        '服務據點',
        '隱私政策',
        '聯絡我們'
      ]
    },
    {
      title: '支援服務',
      links: [
        '保固註冊',
        '售後服務',
        '客服中心'
      ]
    },
    {
      title: '會員專區',
      links: [
        '會員權益',
        '訂閱電子報',
        '加入會員'
      ]
    },
    {
      title: '合作方案',
      links: [
        '異業合作',
        '校園合作'
      ]
    }
  ];

  const socialLinks = [
    {
      name: 'Facebook',
      icon: 'https://ext.same-assets.com/940841963/667979677.svg',
      url: '#'
    },
    {
      name: 'YouTube',
      icon: 'https://ext.same-assets.com/940841963/3106345268.svg',
      url: '#'
    },
    {
      name: 'Instagram',
      icon: 'https://ext.same-assets.com/940841963/3239003857.svg',
      url: '#'
    },
    {
      name: 'LINE',
      icon: 'https://ext.same-assets.com/940841963/1261754938.svg',
      url: '#'
    },
    {
      name: 'TikTok',
      icon: 'https://ext.same-assets.com/940841963/2999481514.svg',
      url: '#'
    }
  ];

  const brands = [
    {
      name: 'ANKER',
      logo: 'https://ext.same-assets.com/940841963/3792121818.svg'
    },
    {
      name: 'soundcore',
      logo: 'https://ext.same-assets.com/940841963/1623550781.svg'
    },
    {
      name: 'eufy',
      logo: 'https://ext.same-assets.com/940841963/1162102007.svg'
    },
    {
      name: 'NEBULA',
      logo: 'https://ext.same-assets.com/940841963/4228632956.svg'
    }
  ];

  return (
    <footer className="bg-anker-dark text-white">
      <div className="max-w-[1170px] mx-auto px-4 py-12">
        {/* 主要链接区域 */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8 mb-12">
          {footerSections.map((section, index) => (
            <div key={index}>
              <h3 className="font-bold text-lg mb-4 text-anker-cyan">
                {section.title}
              </h3>
              <ul className="space-y-2">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a
                      href="#"
                      className="text-gray-300 hover:text-white transition-colors duration-300 text-sm"
                    >
                      {link}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}

          {/* 社交媒体 */}
          <div>
            <h3 className="font-bold text-lg mb-4 text-anker-cyan">
              追蹤更多消息
            </h3>
            <div className="flex flex-wrap gap-3">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-anker-cyan transition-colors duration-300"
                  aria-label={social.name}
                >
                  <img
                    src={social.icon}
                    alt={social.name}
                    className="w-5 h-5"
                  />
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* 品牌logo区域 */}
        <div className="border-t border-gray-700 pt-8 mb-8">
          <div className="flex flex-wrap justify-center md:justify-start items-center gap-8">
            {brands.map((brand, index) => (
              <img
                key={index}
                src={brand.logo}
                alt={brand.name}
                className="brand-logo h-8 md:h-10 opacity-70 hover:opacity-100"
              />
            ))}
          </div>
        </div>

        {/* 版权信息 */}
        <div className="border-t border-gray-700 pt-6 text-center">
          <p className="text-gray-400 text-sm">
            Anker Innovations Limited 委託 良銨股份有限公司 營運所有物流、客服等相關業務。
          </p>
        </div>
      </div>

      {/* 回到顶部按钮 */}
      <button
        className="fixed bottom-6 right-6 w-12 h-12 bg-anker-cyan rounded-full flex items-center justify-center shadow-lg hover:bg-blue-600 transition-colors duration-300 z-50"
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        aria-label="回到頂部"
      >
        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
      </button>
    </footer>
  );
}
