'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

const carouselItems = [
  {
    id: 1,
    image: 'https://ext.same-assets.com/940841963/3211154084.jpeg',
    title: '新品上市！',
    subtitle: 'EVERFROST 33L / 43L 行動冰箱',
    features: [
      { label: '299WH', desc: '大容量電池' },
      { label: '100W', desc: '大功率充電' },
      { label: '42小時', desc: '持久保冷' },
      { label: '智慧', desc: 'APP控制' }
    ]
  },
  {
    id: 2,
    image: 'https://ext.same-assets.com/940841963/1651599134.jpeg',
    title: '超長時保固',
    subtitle: '2025年特惠活動',
    features: []
  },
  {
    id: 3,
    image: 'https://ext.same-assets.com/940841963/3464742705.jpeg',
    title: 'Qi2 MagGo',
    subtitle: '磁吸無線充電系列',
    features: []
  }
];

export function HeroCarousel() {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselItems.length);
    }, 6000); // 增加到6秒以便更好地查看内容

    return () => clearInterval(timer);
  }, []);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + carouselItems.length) % carouselItems.length);
  };

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % carouselItems.length);
  };

  return (
    <div className="relative w-full h-[400px] md:h-[500px] lg:h-[600px] overflow-hidden">
      <div
        className="flex w-full h-full transition-transform duration-500 ease-in-out"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
      >
        {carouselItems.map((item, index) => (
          <div key={item.id} className="relative w-full h-full flex-shrink-0">
            <div
              className="w-full h-full bg-cover bg-center bg-gradient-to-r from-cyan-400 via-blue-500 to-cyan-600"
              style={{
                backgroundImage: `linear-gradient(135deg, rgba(47, 177, 210, 0.9), rgba(26, 115, 232, 0.8)), url(${item.image})`,
                backgroundBlendMode: 'overlay'
              }}
            >
              <div className="absolute inset-0 flex items-center">
                <div className="max-w-[1170px] mx-auto px-4">
                  <div className="max-w-6xl mx-auto flex items-center justify-between">
                    {/* 左侧文本内容 */}
                    <div className="text-white z-10 max-w-lg">
                      <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight">
                        {item.title}
                      </h1>
                      <p className="text-xl md:text-2xl lg:text-3xl mb-8 font-medium">
                        {item.subtitle}
                      </p>

                      {/* 产品特色 */}
                      {item.features.length > 0 && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                          {item.features.map((feature, idx) => (
                            <div key={idx} className="text-center">
                              <div className="text-2xl md:text-3xl font-bold mb-1">
                                {feature.label}
                              </div>
                              <div className="text-sm md:text-base opacity-90">
                                {feature.desc}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* 右侧产品图片区域 */}
                    <div className="hidden lg:block flex-1 max-w-md">
                      {/* 这里可以放置产品的特写图片 */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 导航按钮 */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 transition-colors"
        onClick={goToPrevious}
      >
        <ChevronLeft className="h-6 w-6" />
      </Button>

      <Button
        variant="ghost"
        size="icon"
        className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:bg-white/20 transition-colors"
        onClick={goToNext}
      >
        <ChevronRight className="h-6 w-6" />
      </Button>

      {/* 底部指示器 */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
        {carouselItems.map((_, index) => (
          <button
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? 'bg-white scale-110'
                : 'bg-white/50 hover:bg-white/70'
            }`}
            onClick={() => goToSlide(index)}
          />
        ))}
      </div>
    </div>
  );
}
