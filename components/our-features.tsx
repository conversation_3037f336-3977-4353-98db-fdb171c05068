import { Award, Shield, Heart } from 'lucide-react';

export function OurFeatures() {
  const features = [
    {
      icon: Award,
      title: '美日銷售首選',
      description: '獲得全球超過千萬位使用者肯定，致力打造自由便利、不設限的智慧科技生活。'
    },
    {
      icon: Shield,
      title: '最安心的超長時保固',
      description: '只要是群光電子總代理之原廠正貨，註冊保固後，提供2年超長時保固（保固期間內故障，提供無差別換貨）\n* 上述保固規則不適用於Eufy品牌之產品'
    },
    {
      icon: Heart,
      title: '各大媒體好評推薦',
      description: '領先全球技術的音頻專利技術（包含SweatGuard、BassUp、ACAA 和 HearID）'
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-[1170px] mx-auto px-4">
        {/* 标题 */}
        <div className="text-center mb-16">
          <p className="text-anker-cyan text-lg font-medium mb-2 tracking-wider">OUR FEATURES</p>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold gradient-text">
            我們的特色
          </h2>
        </div>

        {/* 特色项目 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {features.map((feature, index) => (
            <div key={index} className="text-center group">
              {/* 圆形图标 */}
              <div className="relative mx-auto mb-6">
                <div className="w-24 h-24 md:w-28 md:h-28 bg-gradient-to-br from-anker-cyan to-blue-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                  <feature.icon className="w-10 h-10 md:w-12 md:h-12 text-white" />

                  {/* 数字标识 */}
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md border-2 border-anker-cyan">
                    <span className="text-anker-cyan font-bold text-sm">{index + 1}</span>
                  </div>
                </div>
              </div>

              {/* 标题 */}
              <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-4">
                {feature.title}
              </h3>

              {/* 描述 */}
              <p className="text-gray-600 leading-relaxed text-sm md:text-base max-w-sm mx-auto whitespace-pre-line">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* 产品展示图片 */}
        <div className="mt-16 relative">
          <div className="flex justify-center items-end space-x-4 md:space-x-8">
            <img
              src="https://ext.same-assets.com/940841963/163838551.jpeg"
              alt="Anker Products"
              className="w-full max-w-4xl mx-auto object-contain"
            />
          </div>
        </div>
      </div>
    </section>
  );
}
