export function FeatureCards() {
    const features = [
      {
        id: 1,
        title: '磁吸充電',
        subtitle: 'Qi2 MAGGO',
        image: 'https://ext.same-assets.com/940841963/1897977261.jpeg',
        bgColor: 'from-cyan-400 to-blue-500'
      },
      {
        id: 2,
        title: '新品發布',
        subtitle: 'NEW ARRIVAL',
        image: 'https://ext.same-assets.com/940841963/3019233877.jpeg',
        bgColor: 'from-emerald-400 to-cyan-500'
      },
      {
        id: 3,
        title: '極速快充',
        subtitle: 'GaN CHARGER',
        image: 'https://ext.same-assets.com/940841963/1766402451.jpeg',
        bgColor: 'from-blue-400 to-cyan-500'
      }
    ];
  
    return (
      <section className="py-12 bg-gray-50">
        <div className="max-w-[1170px] mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {features.map((feature) => (
              <div
                key={feature.id}
                className="product-card group relative overflow-hidden rounded-2xl aspect-[4/3] cursor-pointer transform transition-all duration-300 hover:scale-105 hover-shadow"
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgColor} opacity-90`} />
  
                {/* 背景图片 */}
                <div
                  className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                  style={{
                    backgroundImage: `url(${feature.image})`,
                    backgroundBlendMode: 'overlay'
                  }}
                />
  
                {/* 内容覆盖层 */}
                <div className="absolute inset-0 flex flex-col justify-center items-center text-white p-6">
                  <h3 className="text-2xl md:text-3xl font-bold mb-2 text-center">
                    {feature.title}
                  </h3>
                  <p className="text-sm md:text-base font-medium opacity-90 tracking-wider">
                    {feature.subtitle}
                  </p>
                </div>
  
                {/* 悬停效果 */}
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }
  