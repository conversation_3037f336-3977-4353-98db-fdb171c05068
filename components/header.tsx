'use client';

import { useState } from 'react';
import { Search, ShoppingCart, User, Menu, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // 产品分类数据
  const productCategories = [
    {
      title: '行動電源',
      items: [
        { name: '25000mAh 165W', link: '#' },
        { name: '10000mAh 30W', link: '#' },
        { name: 'Qi2 10000mAh', link: '#' },
        { name: '無線行動電源', link: '#' }
      ]
    },
    {
      title: '充電器',
      items: [
        { name: '65W GaNPrime', link: '#' },
        { name: '150W GaN USB-C', link: '#' },
        { name: '300W 充電站', link: '#' },
        { name: '車用充電器', link: '#' }
      ]
    },
    {
      title: '無線充電',
      items: [
        { name: 'Qi2 MagGo 系列', link: '#' },
        { name: '3合1充電座', link: '#' },
        { name: '磁吸無線充電板', link: '#' },
        { name: '車用無線充電', link: '#' }
      ]
    },
    {
      title: '線材配件',
      items: [
        { name: 'USB-C 線材', link: '#' },
        { name: 'Lightning 線材', link: '#' },
        { name: '數據線', link: '#' },
        { name: '轉接頭', link: '#' }
      ]
    },
    {
      title: '戶外電源',
      items: [
        { name: 'EverFrost 行動冰箱', link: '#' },
        { name: 'SOLIX 戶外電源', link: '#' },
        { name: '太陽能板', link: '#' },
        { name: '露營配件', link: '#' }
      ]
    }
  ];

  return (
    <>
      {/* 顶部通知栏 */}
      <div className="bg-anker-dark text-white py-2">
        <div className="max-w-[1170px] mx-auto px-4 text-center text-sm">
          安心客明 A1257行動電源 主動路徑查詢回告
          <button className="ml-auto float-right text-white hover:text-gray-300">
            ✕
          </button>
        </div>
      </div>

      {/* 主导航栏 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-[1170px] mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <img
                src="https://ext.same-assets.com/940841963/731886903.svg"
                alt="ANKER"
                className="h-8"
              />
            </div>

            {/* 桌面导航菜单 */}
            <nav className="hidden lg:flex items-center space-x-8">
              <a href="#" className="text-gray-700 hover:text-anker-cyan transition-colors">新品上市</a>

              {/* 分類總覽 - 带下拉菜单 */}
              <div
                className="relative"
                onMouseEnter={() => setIsDropdownOpen(true)}
                onMouseLeave={() => setIsDropdownOpen(false)}
              >
                <button className="flex items-center text-gray-700 hover:text-anker-cyan transition-colors">
                  分類總覽
                  <ChevronDown className="ml-1 h-4 w-4" />
                </button>

                {/* 下拉菜单 */}
                {isDropdownOpen && (
                  <div className="absolute top-full left-0 mt-2 w-[800px] bg-white border border-gray-200 rounded-lg shadow-xl z-50">
                    <div className="grid grid-cols-5 gap-6 p-6">
                      {productCategories.map((category, index) => (
                        <div key={index} className="space-y-3">
                          <h3 className="font-bold text-anker-cyan text-sm">
                            {category.title}
                          </h3>
                          <ul className="space-y-2">
                            {category.items.map((item, itemIndex) => (
                              <li key={itemIndex}>
                                <a
                                  href={item.link}
                                  className="text-gray-600 hover:text-anker-cyan text-sm transition-colors block"
                                >
                                  {item.name}
                                </a>
                              </li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <a href="#" className="text-gray-700 hover:text-anker-cyan transition-colors">本月精選</a>
            </nav>

            {/* 右侧操作区 */}
            <div className="flex items-center space-x-4">
              {/* 搜索 */}
              <Button variant="ghost" size="sm" className="hidden md:flex">
                <Search className="h-4 w-4" />
                <span className="ml-2 hidden lg:block">搜尋</span>
              </Button>

              {/* 用户 */}
              <Button variant="ghost" size="sm" className="hidden md:flex">
                <User className="h-4 w-4" />
                <span className="ml-2 hidden lg:block">登入/註冊</span>
              </Button>

              {/* 购物车 */}
              <Button variant="ghost" size="sm" className="relative">
                <ShoppingCart className="h-4 w-4" />
                <span className="ml-2 hidden lg:block">購物車(0)</span>
                <span className="absolute -top-1 -right-1 bg-anker-cyan text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  0
                </span>
              </Button>

              {/* 服务链接 */}
              <div className="hidden lg:flex items-center space-x-2 text-sm">
                <a href="#" className="text-gray-600 hover:text-anker-cyan">服務據點</a>
                <span className="text-gray-300">|</span>
                <a href="#" className="text-gray-600 hover:text-anker-cyan">超長時保固</a>
              </div>

              {/* 移动端菜单按钮 */}
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                <Menu className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* 移动端菜单 */}
          {isMenuOpen && (
            <div className="lg:hidden border-t bg-white py-4">
              <nav className="flex flex-col space-y-4">
                <a href="#" className="text-gray-700 hover:text-anker-cyan transition-colors">新品上市</a>
                <a href="#" className="text-gray-700 hover:text-anker-cyan transition-colors">分類總覽</a>
                <a href="#" className="text-gray-700 hover:text-anker-cyan transition-colors">本月精選</a>
                <div className="flex items-center space-x-4 pt-4 border-t">
                  <Button variant="ghost" size="sm">
                    <Search className="h-4 w-4 mr-2" />
                    搜尋
                  </Button>
                  <Button variant="ghost" size="sm">
                    <User className="h-4 w-4 mr-2" />
                    登入
                  </Button>
                </div>
              </nav>
            </div>
          )}
        </div>
      </header>
    </>
  );
}
