export function NewsSection() {
    const news = [
      {
        id: 1,
        date: '2022.12.29',
        title: '不能只有手機好看！「這款」玩色行動電源，打造你的專屬潮時尚',
        image: 'https://ext.same-assets.com/940841963/466098487.jpeg',
        category: '產品推薦'
      },
      {
        id: 2,
        date: '2022.10.20',
        title: '電力救援神器開箱！Anker MagGo 磁吸充電器完美配搭 iPhone 14',
        image: 'https://ext.same-assets.com/940841963/3013969009.jpeg',
        category: '開箱評測'
      }
    ];
  
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-[1170px] mx-auto px-4">
          {/* 标题 */}
          <div className="text-center mb-12">
            <p className="text-anker-cyan text-lg font-medium mb-2 tracking-wider">NEWS</p>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900">
              品牌快報
            </h2>
          </div>
  
          {/* 新闻卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {news.map((item) => (
              <article
                key={item.id}
                className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
              >
                {/* 图片区域 */}
                <div className="relative overflow-hidden">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-48 md:h-64 object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-anker-cyan text-white px-3 py-1 rounded-full text-sm font-medium">
                      {item.category}
                    </span>
                  </div>
                </div>
  
                {/* 内容区域 */}
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    <div className="w-1 h-6 bg-anker-cyan rounded mr-3"></div>
                    <time className="text-anker-cyan font-medium text-lg">
                      {item.date}
                    </time>
                  </div>
  
                  <h3 className="text-xl font-bold text-gray-900 leading-relaxed group-hover:text-anker-cyan transition-colors">
                    {item.title}
                  </h3>
  
                  <div className="mt-4 flex items-center text-anker-cyan">
                    <span className="text-sm font-medium">閱讀更多</span>
                    <svg
                      className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </article>
            ))}
          </div>
  
          {/* 查看更多按钮 */}
          <div className="text-center mt-12">
            <button className="bg-anker-cyan text-white px-8 py-3 rounded-full font-medium hover:bg-blue-600 transition-colors duration-300 shadow-lg hover:shadow-xl">
              查看更多新聞
            </button>
          </div>
        </div>
      </section>
    );
  }
  