export function BrandSection() {
    const brands = [
      {
        name: 'ANKER',
        logo: 'https://ext.same-assets.com/940841963/3792121818.svg',
        description: '充電配件領導品牌'
      },
      {
        name: 'soundcore',
        logo: 'https://ext.same-assets.com/940841963/1623550781.svg',
        description: '音頻科技專家'
      },
      {
        name: 'eufy',
        logo: 'https://ext.same-assets.com/940841963/1162102007.svg',
        description: '智能家居解決方案'
      },
      {
        name: 'NEBULA',
        logo: 'https://ext.same-assets.com/940841963/4228632956.svg',
        description: '投影設備創新者'
      }
    ];
  
    return (
      <section className="py-16 bg-white">
        <div className="max-w-[1170px] mx-auto px-4">
          {/* 标题 */}
          <div className="text-center mb-12">
            <p className="text-anker-cyan text-lg font-medium mb-2 tracking-wider">BRAND</p>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-8">
              主打品牌
            </h2>
          </div>
  
          {/* 品牌展示 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            {brands.map((brand, index) => (
              <div
                key={index}
                className="group text-center p-6 rounded-2xl hover:bg-gray-50 transition-all duration-300 cursor-pointer"
              >
                <div className="flex justify-center mb-4">
                  <img
                    src={brand.logo}
                    alt={brand.name}
                    className="h-12 md:h-16 object-contain group-hover:scale-110 transition-transform duration-300"
                  />
                </div>
                <p className="text-gray-600 text-sm group-hover:text-anker-cyan transition-colors">
                  {brand.description}
                </p>
              </div>
            ))}
          </div>
  
          {/* 背景区域 */}
          <div className="h-32 md:h-48 bg-gradient-to-r from-cyan-100 to-blue-100 rounded-2xl flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-xl md:text-2xl font-bold text-gray-700 mb-2">
                探索更多 ANKER 生態系產品
              </h3>
              <p className="text-gray-600">
                為您的生活帶來更多便利與創新
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }
  