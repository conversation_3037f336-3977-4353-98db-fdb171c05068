# PayPal 订阅功能集成指南

本指南将帮助您在项目中设置PayPal订阅功能。

## 1. PayPal开发者账户设置

### 1.1 创建PayPal开发者账户
1. 访问 [PayPal Developer](https://developer.paypal.com/)
2. 使用您的PayPal账户登录或创建新账户
3. 进入开发者控制台

### 1.2 创建应用程序
1. 在开发者控制台中，点击 "Create App"
2. 选择应用类型为 "Default Application"
3. 选择商家账户（或创建新的沙盒商家账户）
4. 在功能选择中，确保勾选：
   - **Subscriptions** (订阅功能)
   - **Payments** (支付功能)

### 1.3 获取API凭据
创建应用后，您将获得：
- **Client ID** (客户端ID)
- **Client Secret** (客户端密钥)

分别获取沙盒和生产环境的凭据。

## 2. 创建产品和订阅计划

### 2.1 创建产品
1. 在PayPal开发者控制台中，进入您的应用
2. 使用PayPal API或控制台创建产品：
   ```bash
   curl -X POST https://api-m.sandbox.paypal.com/v1/catalogs/products \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -d '{
       "name": "SaaS Subscription Service",
       "description": "AI Image Generation Service",
       "type": "SERVICE",
       "category": "SOFTWARE"
     }'
   ```

### 2.2 记录产品ID
创建产品后，记录返回的产品ID，这将用于环境变量 `PAYPAL_PRODUCT_ID`。

## 3. 环境变量配置

在您的 `.env.local` 文件中添加以下配置：

```env
# PayPal 沙盒环境（开发）
PAYPAL_SANDBOX_CLIENT_ID="your_sandbox_client_id"
PAYPAL_SANDBOX_CLIENT_SECRET="your_sandbox_client_secret"

# PayPal 生产环境
PAYPAL_CLIENT_ID="your_production_client_id"
PAYPAL_CLIENT_SECRET="your_production_client_secret"

# PayPal 产品ID
PAYPAL_PRODUCT_ID="your_product_id"
```

## 4. Webhook配置

### 4.1 设置Webhook端点
1. 在PayPal开发者控制台中，进入您的应用
2. 在 "Webhooks" 部分，添加新的webhook
3. 设置webhook URL：
   - 开发环境: `https://your-domain.com/api/paypal/webhook`
   - 生产环境: `https://your-production-domain.com/api/paypal/webhook`

### 4.2 选择事件类型
选择以下事件类型：
- `BILLING.SUBSCRIPTION.ACTIVATED`
- `BILLING.SUBSCRIPTION.CANCELLED`
- `BILLING.SUBSCRIPTION.SUSPENDED`
- `BILLING.SUBSCRIPTION.PAYMENT.FAILED`
- `PAYMENT.SALE.COMPLETED`

## 5. 数据库迁移

运行数据库迁移以添加PayPal相关字段：

```bash
pnpm db:migrate
```

## 6. 测试订阅功能

### 6.1 沙盒测试
1. 确保使用沙盒凭据
2. 访问 `/subscription` 页面
3. 选择订阅计划
4. 使用PayPal沙盒测试账户完成支付

### 6.2 测试账户
在PayPal沙盒中创建测试买家账户：
- 邮箱: <EMAIL>
- 密码: 设置一个测试密码

## 7. 生产环境部署

### 7.1 切换到生产凭据
1. 将环境变量更新为生产凭据
2. 确保 `NODE_ENV=production`
3. 更新webhook URL为生产域名

### 7.2 验证配置
1. 测试订阅创建流程
2. 验证webhook接收
3. 检查数据库记录更新

## 8. 功能特性

### 8.1 支持的订阅计划
- **Premium Plan**: $9.99/月，1000积分
- **Pro Plan**: $29.99/月，5000积分

### 8.2 主要功能
- ✅ 创建PayPal订阅
- ✅ 处理支付成功/失败
- ✅ 自动积分重置
- ✅ 订阅取消
- ✅ Webhook事件处理
- ✅ 订阅状态管理

## 9. API端点

- `POST /api/paypal/subscription/create` - 创建订阅
- `GET /api/paypal/subscription/success` - 支付成功回调
- `GET /api/paypal/subscription/cancel` - 支付取消回调
- `POST /api/paypal/webhook` - Webhook处理
- `GET /api/paypal/subscription/manage` - 获取订阅详情
- `DELETE /api/paypal/subscription/manage` - 取消订阅

## 10. 故障排除

### 10.1 常见问题
1. **订阅创建失败**: 检查PayPal凭据和产品ID
2. **Webhook未接收**: 验证webhook URL和事件类型配置
3. **支付失败**: 检查沙盒测试账户余额

### 10.2 调试技巧
1. 查看浏览器控制台错误
2. 检查服务器日志
3. 使用PayPal开发者工具监控API调用

## 11. 安全注意事项

1. **永远不要在前端暴露Client Secret**
2. **验证所有webhook事件的真实性**
3. **使用HTTPS进行所有API调用**
4. **定期轮换API密钥**

## 12. 支持和文档

- [PayPal Developer Documentation](https://developer.paypal.com/docs/)
- [PayPal Subscriptions API](https://developer.paypal.com/docs/subscriptions/)
- [PayPal Webhooks Guide](https://developer.paypal.com/docs/api/webhooks/)
