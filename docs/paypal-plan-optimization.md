# PayPal Plan Optimization

## 概述

本项目已优化PayPal订阅计划的创建和管理，实现了plan的复用功能，避免重复创建相同配置的订阅计划。

## 优化前的问题

- 每次用户订阅时都会创建新的PayPal plan
- 相同配置的plan被重复创建，造成资源浪费
- 增加了不必要的API调用

## 优化后的解决方案

### 1. 新增数据库表

创建了 `PaypalPlan` 表来存储和管理PayPal plans：

```sql
CREATE TABLE "PaypalPlan" (
  "id" text PRIMARY KEY NOT NULL,
  "planType" text NOT NULL UNIQUE,
  "paypalPlanId" text NOT NULL UNIQUE,
  "name" text NOT NULL,
  "description" text NOT NULL,
  "price" text NOT NULL,
  "currency" text NOT NULL,
  "interval" text NOT NULL,
  "credits" integer NOT NULL,
  "isActive" boolean DEFAULT true NOT NULL,
  "createdAt" timestamp DEFAULT now() NOT NULL,
  "updatedAt" timestamp DEFAULT now() NOT NULL
);
```

### 2. 新增函数

#### `getOrCreatePayPalPlan(planType)`

这个函数实现了plan的复用逻辑：

1. 首先查询数据库中是否已存在相同类型的活跃plan
2. 如果存在，直接返回已有的plan ID
3. 如果不存在，创建新的plan并保存到数据库

```javascript
const planResult = await getOrCreatePayPalPlan('PREMIUM');
if (planResult.success) {
  console.log('Plan ID:', planResult.planId);
  console.log('Is new plan:', planResult.isNew);
}
```

### 3. 更新的文件

- `lib/db/schema.ts` - 添加了 `paypalPlans` 表定义
- `lib/paypal/subscriptions.ts` - 添加了 `getOrCreatePayPalPlan` 函数
- `app/api/paypal/subscription/create/route.ts` - 使用新的复用逻辑
- `app/api/paypal/webhook/route.ts` - 改进了plan信息获取逻辑

### 4. 工作流程

#### 用户订阅流程：
1. 用户选择订阅计划（PREMIUM 或 PRO）
2. 系统调用 `getOrCreatePayPalPlan(planType)`
3. 函数检查数据库中是否已有该类型的plan
4. 如果有，复用现有plan；如果没有，创建新plan并保存
5. 使用plan ID创建PayPal订阅

#### Webhook处理：
1. 接收PayPal webhook事件
2. 通过 `paypalPlanId` 查询数据库获取plan详情
3. 根据plan配置重置用户积分

## 优势

1. **减少API调用**：避免重复创建相同的plan
2. **提高性能**：减少PayPal API的调用次数
3. **数据一致性**：统一管理所有plan信息
4. **易于维护**：plan信息集中存储，便于查询和管理

## 管理工具

### 查看所有Plans
```bash
node scripts/list-paypal-plans.js
```

### 测试Plan复用功能
```bash
node test-paypal-plan-reuse.js
```

## 注意事项

1. 确保数据库迁移已正确执行
2. 现有的订阅不会受到影响
3. 如果需要修改plan配置，建议创建新的plan类型而不是修改现有的
4. 定期检查inactive的plans，可以考虑清理

## 数据库关系

```
PaypalPlan (1) ←→ (N) Subscription
```

每个订阅记录通过 `paypalPlanId` 字段关联到对应的PayPal plan。
