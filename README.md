# SaaS Starter

一个现代化的SaaS平台启动模板，基于Next.js构建，包含完整的认证系统和UI组件库。

## 特性

- **Next.js App Router** - 使用最新的Next.js路由架构
- **认证系统** - 集成NextAuth，支持多种认证方式
- **UI组件库** - 使用shadcn/ui构建现代化UI
- **数据库** - 使用Drizzle ORM管理PostgreSQL数据库
- **样式管理** - 使用TailwindCSS + class-variance-authority
- **类型安全** - 使用TypeScript开发

## 技术栈

- **前端**: Next.js (15.3.1), React (19.1.0), TypeScript (5.x)
- **样式**: TailwindCSS (4.x), class-variance-authority (0.7.1), tailwind-merge (3.2.0), clsx (2.1.1)
- **认证**: NextAuth (5.0.0-beta.28)
- **数据库**: Drizzle ORM (0.36.4), PostgreSQL
- **UI组件**: shadcn/ui, <PERSON><PERSON><PERSON> (1.1.2), Lucide <PERSON>act (0.483.0)
- **AI**: Fal.ai (1.0.0), OpenAI (4.98.0), Replicate (1.0.1)
- **数据验证**: Zod (3.24.2)
- **动画**: TailwindCSS Animate (1.0.7)
- **提示**: Sonner (2.0.3)
- **加密**: bcryptjs (3.0.2)

## 快速开始

1. 克隆仓库
   ```bash
   git clone https://github.com/bestguozi/saas-starter.git
   cd saas-starter
   ```

2. 安装依赖
   ```bash
   pnpm install
   ```

3. 配置环境变量
   复制`.env.example`到`.env`并填写相关配置

4. 数据库迁移
   ```bash
   pnpm db:push
   ```

5. 启动开发服务器
   ```bash
   pnpm dev
   ```

## 项目结构

```
saas-starter/
├── app/                # Next.js App Router
│   ├── api/            # API路由
│   ├── login/          # 登录页面
│   └── page.tsx        # 主页面
├── components/         # UI组件
│   └── ui/             # shadcn/ui组件
├── lib/                # 工具库
├── drizzle/            # Drizzle配置和迁移
└── public/             # 静态资源
```

## 开发指南

1. **组件开发**
   - 使用shadcn/ui组件库
   - 遵循原子设计原则
   - 使用class-variance-authority管理组件变体

2. **样式管理**
   - 使用TailwindCSS
   - 通过tailwind-merge合并类名
   - 使用clsx条件渲染类名

3. **认证开发**
   - 使用NextAuth配置认证
   - 通过Drizzle ORM连接数据库
   - 支持自定义认证流程

## 贡献指南

欢迎提交Pull Request。请确保：
- 代码符合项目风格
- 包含必要的测试
- 更新相关文档

## PayPal 订阅功能

项目已集成PayPal订阅功能，支持：

- ✅ PayPal订阅创建和管理
- ✅ 多种订阅计划（Premium/Pro）
- ✅ 自动积分重置
- ✅ Webhook事件处理
- ✅ 订阅状态管理

详细设置指南请参考 [PAYPAL_SETUP.md](./PAYPAL_SETUP.md)

### 快速开始

1. 配置PayPal环境变量
2. 运行数据库迁移：`pnpm db:migrate`
3. 访问 `/subscription` 页面测试功能

## 许可证

MIT License
