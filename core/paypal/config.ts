// PayPal configuration
const isProduction = process.env.NODE_ENV === 'production';

const clientId = isProduction
  ? process.env.PAYPAL_CLIENT_ID!
  : process.env.PAYPAL_SANDBOX_CLIENT_ID!;

const clientSecret = isProduction
  ? process.env.PAYPAL_CLIENT_SECRET!
  : process.env.PAYPAL_SANDBOX_CLIENT_SECRET!;

// PayPal API base URL
export const PAYPAL_API_BASE = isProduction
  ? 'https://api-m.paypal.com'
  : 'https://api-m.sandbox.paypal.com';

// Get PayPal access token
export async function getPayPalAccessToken(): Promise<string> {
  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

  const response = await fetch(`${PAYPAL_API_BASE}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials',
  });

  if (!response.ok) {
    throw new Error('Failed to get PayPal access token');
  }

  const data = await response.json();
  return data.access_token;
}

// PayPal subscription plans configuration
export const PAYPAL_PLANS = {
  PREMIUM: {
    name: 'Premium Plan',
    description: 'Premium subscription with 1000 credits per month',
    price: '9.99',
    currency: 'USD',
    interval: 'MONTH',
    credits: 1000,
  },
  PRO: {
    name: 'Pro Plan', 
    description: 'Pro subscription with 5000 credits per month',
    price: '29.99',
    currency: 'USD',
    interval: 'MONTH',
    credits: 5000,
  }
} as const;

export type PayPalPlanType = keyof typeof PAYPAL_PLANS;
