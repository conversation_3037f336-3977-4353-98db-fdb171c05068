import { getPayPalAccessToken, PAYPAL_API_BASE, PAYPAL_PLANS, PayPalPlanType } from './config';
import { database } from '@/core/db/index';
import { subscriptions, paypalPlans } from '@/core/db/schema';
import { eq } from 'drizzle-orm';
import { createId } from '@paralleldrive/cuid2';

// Re-export types
export type { PayPalPlanType };

// Get or create PayPal plan (reuse existing plans)
export async function getOrCreatePayPalPlan(planType: PayPalPlanType) {
  try {
    // First, check if we already have this plan in our database
    const existingPlan = await database
      .select()
      .from(paypalPlans)
      .where(eq(paypalPlans.planType, planType))
      .limit(1);

    if (existingPlan.length > 0 && existingPlan[0].isActive) {
      // Plan already exists, return it
      return {
        success: true,
        planId: existingPlan[0].paypalPlanId,
        isNew: false,
        data: existingPlan[0],
      };
    }

    // Plan doesn't exist, create a new one
    const planResult = await createPayPalPlan(planType);

    if (!planResult.success) {
      return planResult;
    }

    // Save the new plan to our database
    const plan = PAYPAL_PLANS[planType];
    await database.insert(paypalPlans).values({
      id: createId(),
      planType: planType,
      paypalPlanId: planResult.planId!,
      name: plan.name,
      description: plan.description,
      price: plan.price,
      currency: plan.currency,
      interval: plan.interval,
      credits: plan.credits,
      isActive: true,
    });

    return {
      success: true,
      planId: planResult.planId!,
      isNew: true,
      data: planResult.data,
    };
  } catch (error) {
    console.error('Error getting or creating PayPal plan:', error);
    return {
      success: false,
      error: 'Failed to get or create PayPal plan',
    };
  }
}

// Create PayPal subscription plan
export async function createPayPalPlan(planType: PayPalPlanType) {
  try {
    const plan = PAYPAL_PLANS[planType];
    const accessToken = await getPayPalAccessToken();

    const requestBody = {
      product_id: process.env.PAYPAL_PRODUCT_ID!, // You need to create a product in PayPal dashboard
      name: plan.name,
      description: plan.description,
      status: 'ACTIVE',
      billing_cycles: [
        {
          frequency: {
            interval_unit: plan.interval,
            interval_count: 1,
          },
          tenure_type: 'REGULAR',
          sequence: 1,
          total_cycles: 0, // 0 means infinite
          pricing_scheme: {
            fixed_price: {
              value: plan.price,
              currency_code: plan.currency,
            },
          },
        },
      ],
      payment_preferences: {
        auto_bill_outstanding: true,
        setup_fee_failure_action: 'CONTINUE',
        payment_failure_threshold: 3,
      },
      taxes: {
        percentage: '0',
        inclusive: false,
      },
    };

    const response = await fetch(`${PAYPAL_API_BASE}/v1/billing/plans`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Prefer': 'return=representation',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('PayPal API Error:', errorData);
      throw new Error(`PayPal API Error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      planId: data.id,
      data: data,
    };
  } catch (error) {
    console.error('Error creating PayPal plan:', error);
    return {
      success: false,
      error: 'Failed to create PayPal plan',
    };
  }
}

// Create PayPal subscription
export async function createPayPalSubscription(userId: string, planId: string, planType: PayPalPlanType) {
  try {
    const accessToken = await getPayPalAccessToken();

    const requestBody = {
      plan_id: planId,
      start_time: new Date(Date.now() + 60000).toISOString(), // Start 1 minute from now
      subscriber: {
        name: {
          given_name: 'User',
          surname: 'Name',
        },
      },
      application_context: {
        brand_name: 'Your SaaS App',
        locale: 'en-US',
        shipping_preference: 'NO_SHIPPING',
        user_action: 'SUBSCRIBE_NOW',
        payment_method: {
          payer_selected: 'PAYPAL',
          payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED',
        },
        return_url: `${process.env.NEXTAUTH_URL}/api/paypal/subscription/success`,
        cancel_url: `${process.env.NEXTAUTH_URL}/api/paypal/subscription/cancel`,
      },
    };

    const response = await fetch(`${PAYPAL_API_BASE}/v1/billing/subscriptions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Prefer': 'return=representation',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('PayPal API Error:', errorData);
      throw new Error(`PayPal API Error: ${response.status}`);
    }

    const data = await response.json();

    if (data.id) {
      // Create subscription record in database
      const plan = PAYPAL_PLANS[planType];
      await database.insert(subscriptions).values({
        id: createId(),
        userId: userId,
        type: 'PREMIUM',
        status: 'INACTIVE', // Will be updated when payment is confirmed
        creditsGrantedPerMonth: plan.credits,
        creditsRemaining: plan.credits,
        paypalSubscriptionId: data.id,
        paypalPlanId: planId,
        paymentProvider: 'paypal',
      });
    }

    return {
      success: true,
      subscriptionId: data.id,
      approvalUrl: data.links?.find((link: any) => link.rel === 'approve')?.href,
      data: data,
    };
  } catch (error) {
    console.error('Error creating PayPal subscription:', error);
    return {
      success: false,
      error: 'Failed to create PayPal subscription',
    };
  }
}

// Get PayPal subscription details
export async function getPayPalSubscription(subscriptionId: string) {
  try {
    const accessToken = await getPayPalAccessToken();

    const response = await fetch(`${PAYPAL_API_BASE}/v1/billing/subscriptions/${subscriptionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('PayPal API Error:', errorData);
      throw new Error(`PayPal API Error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data,
    };
  } catch (error) {
    console.error('Error getting PayPal subscription:', error);
    return {
      success: false,
      error: 'Failed to get PayPal subscription',
    };
  }
}

// Cancel PayPal subscription
export async function cancelPayPalSubscription(subscriptionId: string, reason: string = 'User requested cancellation') {
  try {
    const accessToken = await getPayPalAccessToken();

    const requestBody = {
      reason: reason,
    };

    const response = await fetch(`${PAYPAL_API_BASE}/v1/billing/subscriptions/${subscriptionId}/cancel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('PayPal API Error:', errorData);
      throw new Error(`PayPal API Error: ${response.status}`);
    }

    // Update subscription status in database
    await database.update(subscriptions)
      .set({
        status: 'CANCELED',
        updatedAt: new Date(),
      })
      .where(eq(subscriptions.paypalSubscriptionId, subscriptionId));

    return {
      success: true,
      data: response.status === 204 ? { message: 'Subscription cancelled successfully' } : await response.json(),
    };
  } catch (error) {
    console.error('Error canceling PayPal subscription:', error);
    return {
      success: false,
      error: 'Failed to cancel PayPal subscription',
    };
  }
}

// Activate PayPal subscription (called after successful payment)
export async function activatePayPalSubscription(subscriptionId: string) {
  try {
    const plan = PAYPAL_PLANS.PREMIUM; // Default to premium, you might want to get this from the subscription
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());

    await database.update(subscriptions)
      .set({
        status: 'ACTIVE',
        currentPeriodStart: now,
        currentPeriodEnd: nextMonth,
        updatedAt: now,
      })
      .where(eq(subscriptions.paypalSubscriptionId, subscriptionId));

    return {
      success: true,
    };
  } catch (error) {
    console.error('Error activating PayPal subscription:', error);
    return {
      success: false,
      error: 'Failed to activate PayPal subscription',
    };
  }
}
