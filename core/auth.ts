import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { database } from "./db/index";
import { users, subscriptions} from "./db/schema";
import { compare } from "bcryptjs";
import Github from "next-auth/providers/github";
import Google from "next-auth/providers/google";
import { verifyTurnstile } from "./lib/turnstile";
import { eq, and } from "drizzle-orm";
import { getUserByEmail, createNewOauthUser } from "./services/users";

export const { handlers, auth, signIn, signOut} = NextAuth({
    providers: [
        CredentialsProvider({
            name: "Credentials",
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
                token: { label: "Token", type: "hidden" },
            },
            async authorize(credentials) {

                if (!credentials?.email || !credentials?.password || !credentials?.token) {
                    return null
                }
                //check turnstile token
                
                try{
                    const isTurnstileValid = await verifyTurnstile(credentials?.token as string)
                    if (!isTurnstileValid) {
                        return null
                    }
                    const userResult = await database.select().from(users).where(eq(users.email, credentials?.email as string)).limit(1);
                    const user = userResult[0];
                    if (!user) {
                        return null
                    }
                    const isPasswordValid = await compare(credentials?.password as string, user.passwordHash as string);
                    if (!isPasswordValid) {
                        return null
                    }
                    return user;
                }catch(error){
                    return null
                }
            },
        }),
        Github({
            clientId: process.env.AUTH_GITHUB_ID,
            clientSecret: process.env.AUTH_GITHUB_SECRET,
        }),
        Google({
            clientId: process.env.AUTH_GOOGLE_ID,
            clientSecret: process.env.AUTH_GOOGLE_SECRET,
        })
    ],
    callbacks:{
        signIn: async ({user, account, profile, credentials, email}) => {
            try {
                       
                if (account?.type === "oauth") {
                    const userinfo = await getUserByEmail(user.email as string);
                    if(!userinfo){
                        await createNewOauthUser({
                            email: user.email as string,
                            name: profile?.name as string,
                            image: profile?.image as string,
                            type: account?.type as string,
                            provider: account?.provider as string,
                            providerAccountId: account?.providerAccountId as string,
                            accessToken: account?.access_token as string,
                            refreshToken: account?.refresh_token as string,
                            expiresAt: account?.expires_at as number,
                            tokenType: account?.token_type as string,
                            scope: account?.scope as string,
                            idToken: account?.id_token as string,
                            sessionState: account?.session_state as string,
                        });
                    }
                }
                return true;
            } catch (error) {
                return false; // Explicitly return false on error to prevent login
            }
        },
        //end
        jwt: async ({ token, user }) => {
            if (user) {
                //find user by email
                const userExistResult = await database.select().from(users).where(eq(users.email, user.email as string)).limit(1);
                const userExist = userExistResult[0];
                token.id = userExist?.id as string;
                token.email = user.email as string;
                token.name = user.name as string;
                token.image = user.image as string;
            }
            return token;
        },
        session: async ({ session, token }) => { 
            if (session.user && token.id) { 
                session.user.id = token.id as string; 
                session.user.email = token.email as string;
                session.user.name = token.name as string;
                session.user.image = token.image as string;
            }
            return session;
        },
    }
})