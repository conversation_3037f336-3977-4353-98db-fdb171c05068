'use client'

import * as React from "react"
import { Input } from "@/core/components/ui/input"
import { Label } from "@/core/components/ui/label"
import { Button } from "@/core/components/ui/button"
import { signIn } from "next-auth/react"
import Turnstile, { useTurnstile } from "react-turnstile"
import Link from "next/link"
import GithubButton from "./oauth/github"
import GoogleButton from "./oauth/google"


export function LoginForm() {
  const [error, setError] = React.useState<string | null>(null);
  const [isCredentialsLoading, setIsCredentialsLoading] = React.useState(false);
  const [isGitHubLoading, setIsGitHubLoading] = React.useState(false);
  const [isRegisterDialogOpen, setIsRegisterDialogOpen] = React.useState(false); // 新增 state
  const [turnstileToken, setTurnstileToken] = React.useState<string | null>(null);
  const turnstile = useTurnstile(); // 将 useTurnstile 移到组件顶层

  const handleCredentialsSignIn = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setError(null)
    setIsCredentialsLoading(true)
    const formData = new FormData(event.currentTarget)
    const email = formData.get('email') as string
    const password = formData.get('password') as string
    try {
      const result = await signIn('credentials', {
        email,
        password,
        token: turnstileToken,
        redirect: false,
      })
      console.log(result)
      if (result?.error) {
        if (result.error === "CredentialsSignin") {
          setError("email or password invalid.")
        } else {
          setError(result.error)
        }
      } 
    } catch (err) {
      setError("login error.")
      console.error(err)
    } finally {
      setIsCredentialsLoading(false)
    }
  }

  return (
    <>
      <div className=" w-full sm:w-[400px] mx-auto ">
          
        <div className="space-y-4 py-4">
          {error && (
            <div className="bg-destructive/15 p-3 rounded-md flex items-center gap-x-2 text-sm text-destructive">
              {/* 可以添加一个错误图标 */}
              <p>{error}</p>
            </div>
          )}
          <form onSubmit={handleCredentialsSignIn} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email-dialog">Email</Label>
              <Input 
                id="email-dialog" 
                name="email" 
                type="email" 
                placeholder="<EMAIL>"
                required
                disabled={isCredentialsLoading || isGitHubLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password-dialog">Password</Label>
              <Input
                id="password-dialog"
                name="password"
                type="password"
                required
                disabled={isCredentialsLoading || isGitHubLoading}
              />
            </div>
            <Turnstile
              sitekey={'0x4AAAAAABPToBobzrFbjtye'}
              onVerify={(token) => setTurnstileToken(token)}
            />
            <Button type="submit" className="w-full cursor-pointer" disabled={isCredentialsLoading || isGitHubLoading}>
              {isCredentialsLoading ? "login..." : "Sign In"}
            </Button>
          </form>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or sign in with
              </span>
            </div>
          </div>

          <GithubButton />
          <GoogleButton />
        </div>
        <div className="flex flex-col space-y-2 text-center text-sm text-muted-foreground sm:flex-row sm:justify-center">
          <div>
            no account？
            <Link href="/sign-up">
            <Button
              variant="link"
              className="p-0 h-auto underline cursor-pointer"
              onClick={() => {
                setIsRegisterDialogOpen(true); // 打开注册对话框
              }}
            >
              sign up  
            </Button>
            </Link>
          </div>  
        </div>
      </div>
  </>
  )
}