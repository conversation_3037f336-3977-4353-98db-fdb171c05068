'use client'
import { Button } from '@/core/components/ui/button';
import {useState} from 'react';
import { signIn } from 'next-auth/react';
import { Github } from 'lucide-react';

export default function GithubButton(){
    const [loading, setLoading] = useState(false);
    const handleignIn = async (provider: string) => {
        setLoading(true)
        try {
          // google 登录通常涉及完整页面重定向
          // callbackUrl 设置为当前页面，以便登录后返回
          await signIn(provider, { callbackUrl: window.location.href })
          // 如果 signIn 启动重定向，下面的代码可能不会立即执行
        } catch (err) {
          console.error(err)
          setLoading(false) // 仅在 signIn 本身在重定向前抛出错误时设置
        }
        // 如果发生重定向，组件将在页面重新加载时重新初始化，因此无需在此处设置 setIsGitHubLoading(false)
      }

    return <Button 
                onClick={() => handleignIn('github')} 
                className="w-full cursor-pointer"
                variant="outline"
                disabled={loading}
              >
                <Github className="mr-2 h-4 w-4" />
                {loading ? `redirecting...` : `Sign in with github`}
              </Button>
}