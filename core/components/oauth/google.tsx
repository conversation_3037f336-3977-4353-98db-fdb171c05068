'use client'
import { But<PERSON> } from '@/core/components/ui/button';
import {useState} from 'react';
import { signIn } from 'next-auth/react';

export default function GoogleButton(){
    const [loading, setLoading] = useState(false);
    const handleignIn = async (provider: string) => {
        setLoading(true)
        try {
          // google 登录通常涉及完整页面重定向
          // callbackUrl 设置为当前页面，以便登录后返回
          await signIn(provider, { callbackUrl: window.location.href })
          // 如果 signIn 启动重定向，下面的代码可能不会立即执行
        } catch (err) {
          console.error(err)
          setLoading(false) // 仅在 signIn 本身在重定向前抛出错误时设置
        }
        // 如果发生重定向，组件将在页面重新加载时重新初始化，因此无需在此处设置 setIsGitHubLoading(false)
      }

    return <Button 
                onClick={() => handleignIn('google')} 
                className="w-full cursor-pointer"
                variant="outline"
                disabled={loading}
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
                <path d="M564 325.8C564 467.3 467.1 568 324 568C186.8 568 76 457.2 76 320C76 182.8 186.8 72 324 72C390.8 72 447 96.5 490.3 136.9L422.8 201.8C334.5 116.6 170.3 180.6 170.3 320C170.3 406.5 239.4 476.6 324 476.6C422.2 476.6 459 406.2 464.8 369.7L324 369.7L324 284.4L560.1 284.4C562.4 297.1 564 309.3 564 325.8z"/>
                </svg>
                {loading ? `redirecting...` : `Sign in with google`}
              </Button>
}