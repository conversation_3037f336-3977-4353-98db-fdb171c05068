"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, User, X } from "lucide-react"
import { But<PERSON> } from "@/core/components/ui/button"
import Link from "next/link"
import { useSession } from "next-auth/react"
import Image from "next/image"
  
export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const session = useSession()

  return (
    <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-[#E0E0E0] z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="w-8 h-8 mr-3">
              {/* <svg viewBox="0 0 32 32" className="w-full h-full stroke-black stroke-2 fill-none">
                <circle cx="16" cy="8" r="3" />
                <circle cx="8" cy="20" r="3" />
                <circle cx="24" cy="20" r="3" />
                <path d="M16 11L13 17M16 11L19 17M11 20L13 17M21 20L19 17M13 17H19" />
              </svg> */}
              <Image src="/logo.svg" alt="Logo" width={32} height={32} />
            </div>
            <Link href="/"><span className="text-xl font-semibold">ImageFox</span></Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/free-image-generator" className="text-black hover:text-[#333333] transition-colors">
              Free Image Generator
            </Link>
            <Link href="#features" className="text-black hover:text-[#333333] transition-colors">
              Features
            </Link>
            <Link href="#cases" className="text-black hover:text-[#333333] transition-colors">
              Cases
            </Link>
            <Link href="#pricing" className="text-black hover:text-[#333333] transition-colors">
              Pricing
            </Link>
            <Link href="#faq" className="text-black hover:text-[#333333] transition-colors">
              FAQ
            </Link>
          </div>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {session.data?.user ? (
              <Link href="/profile"><User size={20} /></Link>
            ) : (
              <>
              <Link href="/sign-in"><button className="text-black hover:text-[#333333] transition-colors cursor-pointer">Sign In</button></Link>
              <Link href="/sign-up"><Button className="cursor-pointer bg-[#121212] text-white hover:bg-[#333333] hover:scale-105 transition-all duration-300">
                Sign Up
              </Button></Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <button className="md:hidden" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-white border-t border-[#E0E0E0]"
          >
            <div className="px-4 py-4 space-y-4">
              <a href="/free-image-generator" className="block text-black hover:text-[#333333]">
                Free Image Generator
              </a>
              <a href="#" className="block text-black hover:text-[#333333]">
                Features
              </a>
              <a href="#" className="block text-black hover:text-[#333333]">
                Cases
              </a>
              <a href="#" className="block text-black hover:text-[#333333]">
                Pricing
              </a>
              <a href="#" className="block text-black hover:text-[#333333]">
                FAQ
              </a>
              <div className="pt-4 border-t border-[#E0E0E0] space-y-2">
                {session.data?.user ? (
                  <Link href="/profile">
                    <User size={20} />
                  </Link>
                ) : (
                  <>
                  <Link href="/sign-in"><button className="block w-full text-left text-black">Sign In</button></Link>
                  <Link href="/sign-up"><Button className="w-full bg-[#121212] text-white hover:bg-[#333333]">Sign Up</Button></Link>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  )
}
