import { database } from "@/core/db/index";
import { createId } from "@paralleldrive/cuid2";
import { users, accounts, subscriptions } from "@/core/db/schema";
import { hash } from 'bcryptjs';

const SALT = 10

export async function getUserByEmail(email: string){
    const user = await database.query.users.findFirst({
        where: (users, { eq })=> eq(users.email, email),
        with:{
            subscription: true
        }
    });
    return user ? user: null;
}

export async function createNewUser({email, password}:{email:string, password: string}){
    try{
        const r = await getUserByEmail(email);
        if(r){
            throw new Error('User already exists');
        }
        const passwordHash = await hash(password, SALT);
        const userId = createId();
        const userWithId = { email: email, passwordHash: passwordHash, id: userId, isActive: true };
        const user = await database.insert(users).values(userWithId).returning();
        return {user: user[0], msg: 'ok'};
    }catch(e){
        console.log('createNewUser error:', e);
        if(e instanceof Error){
            return {user: null, msg: e.message};
        }
        return {user: null, msg: ''};
    }
}

export async function createNewOauthUser({
    email, 
    name, 
    image,
    type,
    provider,
    providerAccountId,
    accessToken,
    refreshToken,
    expiresAt,
    tokenType,
    scope,
    idToken,
    sessionState
}:{
    email: string, 
    name:string, 
    image:string,
    type: string,
    provider: string,
    providerAccountId: string,
    accessToken: string,
    refreshToken: string,
    expiresAt: number,
    tokenType: string,
    scope: string,
    idToken: string,
    sessionState: string
}){
    try{
    const newUserId = createId();
    const user = await database.insert(users).values({
        id: newUserId,
        email: email,
        name: name,
        image: image
    }).returning();

    await database.insert(accounts).values({
        userId: newUserId,
        type: type,
        provider: provider,
        providerAccountId: providerAccountId,
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_at: expiresAt,
        token_type: tokenType,
        scope: scope,
        id_token: idToken,
        session_state: sessionState,
    });

    return {user: user[0], msg: 'ok'};

    }catch(e){
        console.log(e);
        if(e instanceof Error){
            return {user: null, msg: e.message};
        }
        return {user: null, msg: ''};
    }       
}
