'use server'

export async function verifyTurnstile(token:string){
    try{
    const url='https://challenges.cloudflare.com/turnstile/v0/siteverify'
    const response = await fetch(url,{
        method:'POST',
        headers:{
            'Content-Type':'application/json'
        },
        body: JSON.stringify({
            secret:process.env.TURNSTILE_SECRET_KEY,
            response:token
        })
    })
    const data = await response.json()

    return data.success ? true: false;
    }catch(error){
        console.log('verifyTurnstile error:', error)
        return false
    }
}