'use server'
import { Resend } from 'resend';
import EmailTemplate from './email';

const resend = new Resend(process.env.RESEND_API_KEY);


export async function sendEmail(email: string, subject: string, message: string) {
    const result = EmailTemplate({message});
    try {
        const {data, error} = await resend.emails.send({
            from: 'PhotoAI <<EMAIL>>',
            to: [email],
            subject: subject,
            react: result,
        });
        if(error){
            return { success: false, error: error.message };
        }
        return { success: true, messageId: data.id };
    } catch (err) {
        if (err instanceof Error) {
            console.error('Error sending email:', err.message);
            console.error('Stack trace:', err.stack);
            return { success: false, error: err.message };
        }
        console.error('An unknown error occurred while sending email');
        return { success: false, error: 'An unknown error occurred' };
    }
}
    